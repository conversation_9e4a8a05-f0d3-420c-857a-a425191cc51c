# 掐指一算 - 小六壬算命网站

一个基于传统小六壬算法的现代化算命网站，结合古老智慧与现代设计。

## ✨ 功能特色

### 🔮 核心功能
- **六种问题类型**: 问运势、问财富、问感情、问事业、问身体、问行人
- **传统算法**: 基于正宗小六壬六神推演（大安、流连、速喜、赤口、小吉、空亡）
- **智能解读**: 针对不同问题类型提供专业解释
- **手指掐算**: 提供传统手指掐算位置参考图

### 🎨 用户体验
- **流畅动画**: 计算过程逐步展示，带有优雅的动画效果
- **自动滚动**: 智能引导用户浏览计算过程和结果
- **进度指示**: 实时显示计算进度和完成状态
- **滚动指示器**: 右侧导航指示器，支持快速跳转
- **响应式设计**: 完美适配桌面和移动设备

### 🎯 交互优化
- **平滑滚动**: 点击算命后自动滚动到计算过程，完成后滚动到结果
- **视觉反馈**: 按钮选中状态、悬停效果、加载状态等
- **防重复操作**: 计算过程中自动禁用按钮，防止重复提交
- **状态管理**: 智能清理和重置，支持多次使用

## 🛠️ 技术实现

### 前端技术
- **HTML5**: 语义化结构，良好的可访问性
- **CSS3**: 现代化样式，渐变背景，动画效果
- **JavaScript ES6+**: 模块化设计，面向对象编程
- **响应式设计**: 移动优先，适配各种屏幕尺寸

### 性能优化
- **硬件加速**: 使用CSS3 transform优化动画性能
- **DOM优化**: DocumentFragment批量操作，减少重排重绘
- **动画优化**: requestAnimationFrame确保流畅动画
- **滚动优化**: Intersection Observer智能滚动检测

### 算法核心
- **小六壬算法**: 传统六神轮转推演
- **数字映射**: 用户输入数字到神位的精确计算
- **结果分析**: 多维度解读，包含基本含义和针对性建议

## 📱 使用方法

1. **选择问题**: 点击六个问题类型中的任意一个
2. **输入数字**: 填写三个数字（建议1-999之间）
3. **开始算命**: 点击"开始算命"按钮
4. **查看过程**: 自动滚动到计算过程，观看逐步推演
5. **查看结果**: 自动滚动到最终结果，获得详细解读

## 🎨 设计特色

### 视觉设计
- **渐变背景**: 多层次径向渐变，营造神秘氛围
- **毛玻璃效果**: backdrop-filter模糊效果，现代感十足
- **卡片设计**: 统一的卡片风格，层次分明
- **配色方案**: 蓝紫色主调，温暖的辅助色彩

### 动画效果
- **交替动画**: 计算步骤从左右两侧交替滑入
- **脉冲效果**: 计算指示器的呼吸灯效果
- **悬停反馈**: 丰富的鼠标悬停交互效果
- **平滑过渡**: 所有状态变化都有流畅的过渡动画

## 📂 文件结构

```
掐指一算/
├── index.html          # 主页面
├── style.css           # 样式文件
├── script.js           # 核心逻辑
├── 1.png              # 手指掐算参考图
└── README.md          # 项目说明
```

## 🔧 核心代码

### 小六壬算法核心
```javascript
class LiuRenCalculator {
    calculatePosition(number, startIndex = 0) {
        const totalPositions = this.gods.length;
        return (startIndex + number - 1) % totalPositions;
    }
    
    calculateResults(numbers) {
        const results = [];
        let currentIndex = 0;
        
        for (let i = 0; i < numbers.length; i++) {
            const position = this.calculatePosition(numbers[i], currentIndex);
            results.push({
                number: numbers[i],
                god: this.gods[position],
                position: position
            });
            currentIndex = position;
        }
        
        return results;
    }
}
```

### 动画优化
```css
.god-details .god-detail:nth-child(odd) {
    animation: slideInFromLeft 0.6s ease-out forwards;
}

.god-details .god-detail:nth-child(even) {
    animation: slideInFromRight 0.6s ease-out forwards;
}
```

## 🌟 特色亮点

1. **传统与现代结合**: 保持传统小六壬算法的准确性，融入现代化的用户界面
2. **完整的用户流程**: 从选择问题到查看结果的完整引导流程
3. **丰富的视觉反馈**: 每个操作都有相应的视觉反馈和状态提示
4. **性能优化**: 多项技术优化确保流畅的用户体验
5. **响应式适配**: 在各种设备上都能提供良好的体验

## 📝 使用说明

### 问题类型说明
- **问运势**: 询问整体运势走向
- **问财富**: 询问财运和投资相关
- **问感情**: 询问感情和人际关系
- **问事业**: 询问工作和事业发展
- **问身体**: 询问健康状况
- **问行人**: 询问他人消息或行踪

### 数字选择建议
- 建议选择1-999之间的数字
- 可以选择有特殊意义的数字（生日、幸运数字等）
- 三个数字可以相同也可以不同
- 数字大小不影响算命准确性

## ⚠️ 免责声明

此算命结果仅供参考和娱乐，基于传统文化和算法推演。请理性对待结果，不要过分迷信。人生的道路需要靠自己的努力和智慧来开创。

## 🎯 项目目标

通过现代化的技术手段，让传统的小六壬文化得以传承和发扬，为用户提供一个既有文化底蕴又有良好体验的算命平台。

---

*古老智慧，现代解读 - 掐指一算*