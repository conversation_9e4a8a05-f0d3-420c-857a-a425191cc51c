/* 全局滚动性能优化 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 针对滚动优化的全局设置 */
html {
    scroll-behavior: smooth;
}

/* 减少重绘和回流 */
.question-btn,
.number-inputs input,
#calculateBtn,
.finger-position,
.tarot-card {
    contain: layout style;
}

body {
    font-family: 'Microsoft YaHei', 'Noto Sans SC', Arial, sans-serif;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
    color: #333;
    line-height: 1.6;
    overflow-x: hidden;
    /* 滚动优化 */
    scroll-behavior: smooth;
    -webkit-overflow-scrolling: touch;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* 静态背景效果 - 移除动画 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background:
        radial-gradient(circle at 25% 25%, rgba(255, 255, 255, 0.05) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: -1;
}

@keyframes backgroundMove {

    0%,
    100% {
        transform: translateX(0) translateY(0);
    }

    25% {
        transform: translateX(-10px) translateY(-10px);
    }

    50% {
        transform: translateX(10px) translateY(-5px);
    }

    75% {
        transform: translateX(-5px) translateY(10px);
    }
}

/* 头部样式 */
header {
    text-align: center;
    margin-bottom: 50px;
    color: white;
    position: relative;
}

/* 设置按钮样式 */
.settings-btn {
    position: absolute;
    top: 20px;
    right: 20px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    border-radius: 50%;
    width: 44px;
    height: 44px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    color: white;
    transition: all 0.3s ease;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.3);
    z-index: 10;
}

.settings-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: rotate(90deg);
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.settings-btn svg {
    transition: transform 0.3s ease;
}

header::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 100px;
    background: radial-gradient(circle, rgba(255, 215, 0, 0.3) 0%, transparent 70%);
    border-radius: 50%;
    animation: headerGlow 3s ease-in-out infinite;
}

@keyframes headerGlow {

    0%,
    100% {
        opacity: 0.5;
        transform: translateX(-50%) scale(1);
    }

    50% {
        opacity: 1;
        transform: translateX(-50%) scale(1.1);
    }
}

header h1 {
    font-size: 2.8em;
    margin-bottom: 15px;
    text-shadow:
        0 0 20px rgba(255, 215, 0, 0.5),
        0 0 40px rgba(255, 215, 0, 0.3),
        2px 2px 4px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B6B, #4ECDC4);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
    position: relative;
    z-index: 1;
}

@keyframes gradientShift {

    0%,
    100% {
        background-position: 0% 50%;
    }

    50% {
        background-position: 100% 50%;
    }
}

header p {
    font-size: 1.1em;
    opacity: 0.9;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    animation: fadeInUp 1s ease-out 0.5s both;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 0.9;
        transform: translateY(0);
    }
}

/* 主要内容区域 */
main {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.95) 0%, rgba(255, 255, 255, 0.9) 100%);
    border-radius: 25px;
    padding: 50px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.15);
    margin-bottom: 30px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    position: relative;
    overflow: hidden;
}

/* 移除主要内容区的动画背景 */

@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg);
    }

    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg);
    }
}

/* 问题选择区域 */
.question-section {
    margin-bottom: 50px;
    position: relative;
}

.question-section h2 {
    text-align: center;
    margin-bottom: 30px;
    color: #2c3e50;
    font-size: 1.8em;
    font-weight: 300;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.question-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
}

.question-options {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 20px;
    margin-bottom: 20px;
    perspective: 1000px;
    justify-content: center;
}

.question-btn {
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    color: #2c3e50;
    border: 2px solid rgba(102, 126, 234, 0.3);
    padding: 18px 22px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 1em;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    position: relative;
    overflow: hidden;
}

/* 移除光效动画 */

.question-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3);
    border-color: rgba(102, 126, 234, 0.6);
}

.question-btn:active {
    transform: translateY(-1px);
    box-shadow: 0 6px 15px rgba(102, 126, 234, 0.25);
}

.question-btn.selected {
    background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
    color: #fff;
    border-color: rgba(255, 107, 107, 0.6);
    box-shadow: 0 8px 25px rgba(255, 107, 107, 0.3);
    transform: translateY(-2px);
}

.question-btn.selected::after {
    content: '✓';
    position: absolute;
    top: 8px;
    right: 12px;
    color: #fff;
    font-size: 1.2em;
    font-weight: bold;
    animation: checkmarkAppear 0.4s ease-out;
}

@keyframes checkmarkAppear {
    from {
        opacity: 0;
        transform: scale(0) rotate(45deg);
    }

    to {
        opacity: 1;
        transform: scale(1) rotate(0deg);
    }
}

/* 移除过度的动画效果 */

/* 数字输入区域 */
.number-section {
    margin-bottom: 50px;
    text-align: center;
    position: relative;
}

.number-section h2 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.8em;
    font-weight: 300;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.number-section h2::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
}

.number-section .hint {
    color: #7f8c8d;
    margin-bottom: 30px;
    font-size: 1em;
    opacity: 0.8;
    font-weight: 400;
}

.number-inputs {
    display: flex;
    justify-content: center;
    gap: 25px;
    flex-wrap: wrap;
    perspective: 1000px;
}

.number-inputs input {
    width: 120px;
    height: 120px;
    padding: 15px;
    border: 3px solid rgba(102, 126, 234, 0.3);
    border-radius: 20px;
    text-align: center;
    font-size: 1.8em;
    font-weight: bold;
    transition: all 0.3s ease;
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.2);
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    position: relative;
}

.number-inputs input::placeholder {
    color: #bdc3c7;
    font-size: 0.6em;
    font-weight: normal;
}

.number-inputs input:focus {
    outline: none;
    border-color: rgba(102, 126, 234, 0.8);
    box-shadow: 0 12px 30px rgba(102, 126, 234, 0.3), 0 0 0 4px rgba(102, 126, 234, 0.1);
    transform: translateY(-2px);
}

.number-inputs input:hover:not(:focus) {
    transform: translateY(-2px);
    border-color: rgba(102, 126, 234, 0.6);
}

/* 数字输入动画效果 */
.number-inputs input:valid {
    border-color: rgba(46, 204, 113, 0.6);
    box-shadow:
        0 8px 25px rgba(46, 204, 113, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
}

.number-inputs input:valid::after {
    content: '✓';
    position: absolute;
    top: 10px;
    right: 15px;
    color: #27ae60;
    font-size: 1.2em;
    font-weight: bold;
    animation: checkmarkAppear 0.4s ease-out;
}

/* 计算按钮 */
.calculate-section {
    text-align: center;
    margin-bottom: 50px;
    position: relative;
}

#calculateBtn {
    background: linear-gradient(135deg, #00b894 0%, #00cec9 100%);
    color: white;
    border: 3px solid rgba(0, 184, 148, 0.3);
    padding: 20px 40px;
    border-radius: 20px;
    font-size: 1.2em;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 10px 30px rgba(0, 184, 148, 0.3);
    position: relative;
    overflow: hidden;
    min-width: 180px;
}

/* 移除复杂的悬停动画 */

#calculateBtn:hover:not(:disabled) {
    transform: translateY(-3px);
    box-shadow: 0 15px 35px rgba(0, 184, 148, 0.4);
    border-color: rgba(0, 184, 148, 0.8);
}

#calculateBtn:active:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 8px 20px rgba(0, 184, 148, 0.3);
}

#calculateBtn:disabled {
    background: linear-gradient(135deg, #bdc3c7 0%, #95a5a6 100%);
    color: #95a5a6;
    cursor: not-allowed;
    box-shadow: 0 5px 15px rgba(149, 165, 166, 0.2);
    border-color: rgba(149, 165, 166, 0.3);
    transform: none;
}

/* 移除复杂的光效动画 */

/* 手指位置参考 */
.finger-guide {
    margin-bottom: 30px;
    padding: 20px;
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.8) 0%, rgba(255, 255, 255, 0.6) 100%),
        url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="25" cy="25" r="1" fill="%23f0f0f0" opacity="0.3"/><circle cx="75" cy="75" r="1" fill="%23f0f0f0" opacity="0.3"/><circle cx="50" cy="10" r="0.5" fill="%23f0f0f0" opacity="0.3"/><circle cx="10" cy="60" r="0.5" fill="%23f0f0f0" opacity="0.3"/><circle cx="90" cy="40" r="0.5" fill="%23f0f0f0" opacity="0.3"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    border-radius: 15px;
    border: 2px solid rgba(102, 126, 234, 0.2);
    box-shadow:
        0 8px 20px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    position: relative;
    overflow: hidden;
}

.finger-guide::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(102, 126, 234, 0.05), transparent);
    animation: shimmer 4s infinite;
    pointer-events: none;
}

.finger-guide h3 {
    text-align: center;
    color: #2c3e50;
    margin-bottom: 30px;
    font-size: 1.5em;
    font-weight: 300;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.finger-guide h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
}

/* 手指参考图片容器 */
.finger-reference-image {
    text-align: center;
    margin-bottom: 30px;
    position: relative;
}

.finger-reference-image img {
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    border: 3px solid rgba(102, 126, 234, 0.2);
}

.finger-reference-image img:hover {
    transform: translateY(-5px) scale(1.02);
    box-shadow:
        0 20px 40px rgba(0, 0, 0, 0.3),
        0 0 30px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.4);
}

/* 图片加载错误时的样式 */
.finger-reference-image img::error {
    content: '图片加载失败';
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    color: #7f8c8d;
    font-size: 1.1em;
    min-height: 200px;
    border-radius: 15px;
}

.finger-positions {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    gap: 15px;
    perspective: 1000px;
}

.finger-position {
    text-align: center;
    padding: 18px 15px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%);
    border-radius: 12px;
    box-shadow: 0 6px 18px rgba(0, 0, 0, 0.08);
    transition: all 0.3s ease;
    position: relative;
    border: 2px solid rgba(102, 126, 234, 0.2);
}

/* 移除复杂的悬停效果 */

.finger-position:hover {
    transform: translateY(-3px);
    box-shadow: 0 15px 30px rgba(102, 126, 234, 0.2);
    border-color: rgba(102, 126, 234, 0.6);
}

.position-name {
    font-weight: bold;
    color: #2c3e50;
    margin-bottom: 8px;
    font-size: 1.1em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.position-desc {
    color: #7f8c8d;
    font-size: 0.9em;
    opacity: 0.8;
    font-weight: 500;
}

/* 简化手指位置图标 */
.finger-position::after {
    content: '👆';
    position: absolute;
    top: 10px;
    right: 15px;
    font-size: 1.2em;
    opacity: 0.3;
    transition: opacity 0.3s ease;
}

.finger-position:hover::after {
    opacity: 0.7;
}

/* 结果展示区域 */
.result-section {
    margin-top: 50px;
    padding: 20px 0;
    background: transparent;
    border-radius: 0;
    color: #2c3e50;
    position: relative;
}

.result-section h2 {
    text-align: center;
    margin-bottom: 40px;
    font-size: 2.2em;
    font-weight: 300;
    color: #2c3e50;
    position: relative;
}

/* 最终结果容器样式 - 优化配色和性能 */
/* 减少 backdrop-filter 使用以提升滚动性能 */
.final-results-container {
    margin-top: 50px;
    padding: 30px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.96) 0%, rgba(255, 255, 255, 0.92) 100%);
    border-radius: 20px;
    border: 2px solid rgba(116, 185, 255, 0.25);
    box-shadow: 0 15px 35px rgba(116, 185, 255, 0.15);
    position: relative;
    overflow: hidden;
    /* backdrop-filter: blur(8px); 移除以提升性能 */
}

/* 移除复杂的背景动画以提升性能 */

/* 最终结果标题 */
.final-results-header {
    text-align: center;
    margin-bottom: 40px;
    position: relative;
}

.final-results-header h2 {
    color: #2c3e50;
    font-size: 2.2em;
    font-weight: 300;
    margin-bottom: 15px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
}

.final-results-header p {
    color: #7f8c8d;
    font-size: 1.1em;
    line-height: 1.6;
}

/* 神位序列区域 */
.final-sequence-section {
    margin-bottom: 40px;
}

.final-sequence-section h3 {
    color: #2c3e50;
    font-size: 1.5em;
    font-weight: 300;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
}

.final-sequence-section h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #667eea, #764ba2);
    border-radius: 2px;
}

.final-sequence-section .sequence-display {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.final-sequence-section .sequence-item {
    background: linear-gradient(135deg, rgba(116, 185, 255, 0.12) 0%, rgba(108, 92, 231, 0.12) 100%);
    border-radius: 15px;
    padding: 20px;
    min-width: 100px;
    text-align: center;
    box-shadow:
        0 6px 20px rgba(116, 185, 255, 0.18),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(116, 185, 255, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
}

.final-sequence-section .sequence-item:hover {
    transform: translateY(-5px) translateZ(0);
    box-shadow:
        0 12px 28px rgba(116, 185, 255, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.final-sequence-section .sequence-number {
    font-size: 1em;
    color: #74b9ff;
    margin-bottom: 10px;
    font-weight: bold;
}

.final-sequence-section .sequence-god {
    font-weight: bold;
    font-size: 1.2em;
    color: #2c3e50;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* 总体分析区域 */
.final-analysis-section {
    margin-bottom: 40px;
}

.analysis-header {
    display: flex;
    justify-content: center;
    align-items: center;
    position: relative;
    margin-bottom: 20px;
}

.final-analysis-section h3 {
    color: #2c3e50;
    font-size: 1.5em;
    font-weight: 300;
    margin: 0;
    position: relative;
}

/* 小AI按钮样式 */
.ai-answer-btn-small {
    position: absolute;
    right: 0;
    top: 50%;
    transform: translateY(-50%);
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 20px;
    padding: 8px 16px;
    color: white;
    font-size: 0.85em;
    font-weight: 500;
    cursor: pointer;
    display: flex;
    align-items: center;
    gap: 6px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
    white-space: nowrap;
}

.ai-answer-btn-small:hover {
    transform: translateY(-50%) translateY(-2px);
    box-shadow: 0 4px 15px rgba(102, 126, 234, 0.4);
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
}

.ai-answer-btn-small:active {
    transform: translateY(-50%) translateY(0px);
    box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
}

.ai-btn-icon-small {
    font-size: 1em;
    filter: drop-shadow(0 1px 2px rgba(0,0,0,0.2));
}

.ai-btn-text-small {
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0,0,0,0.2);
}

.final-analysis-section h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #00b894, #00cec9);
    border-radius: 2px;
}

.analysis-content {
    background: linear-gradient(135deg, rgba(29, 209, 161, 0.12) 0%, rgba(85, 230, 193, 0.12) 100%);
    border-radius: 15px;
    padding: 25px;
    font-size: 1.1em;
    line-height: 1.8;
    color: #2c3e50;
    box-shadow:
        0 6px 20px rgba(29, 209, 161, 0.18),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(29, 209, 161, 0.25);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
    will-change: transform;
}

/* 详细解释区域 */
.final-details-section h3 {
    color: #2c3e50;
    font-size: 1.5em;
    font-weight: 300;
    margin-bottom: 20px;
    text-align: center;
    position: relative;
}

.final-details-section h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 50px;
    height: 3px;
    background: linear-gradient(90deg, #ff6b6b, #ee5a24);
    border-radius: 2px;
}

.final-details-section .god-details {
    display: grid;
    gap: 20px;
}

.final-details-section .god-detail {
    background: linear-gradient(135deg, rgba(255, 118, 117, 0.12) 0%, rgba(250, 177, 160, 0.12) 100%);
    border-radius: 15px;
    padding: 25px;
    box-shadow:
        0 6px 20px rgba(255, 118, 117, 0.18),
        inset 0 1px 0 rgba(255, 255, 255, 0.7);
    border: 1px solid rgba(255, 118, 117, 0.25);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
}

.final-details-section .god-detail:hover {
    transform: translateY(-3px) translateZ(0);
    box-shadow:
        0 12px 28px rgba(255, 118, 117, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
}

.final-details-section .god-detail h4 {
    color: #2c3e50;
    margin-bottom: 15px;
    font-size: 1.3em;
    font-weight: 400;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.final-details-section .god-nature,
.final-details-section .god-interpretation {
    margin-bottom: 12px;
    line-height: 1.6;
    color: #2c3e50;
}

.final-details-section .god-nature strong,
.final-details-section .god-interpretation strong {
    color: #e74c3c;
    font-weight: 600;
}

.final-details-section .god-interpretation {
    margin-bottom: 0;
}

/* 卡罗牌风格容器 */
.tarot-container {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 30px;
    margin-bottom: 40px;
}

/* 确保最后一行始终显示3个卡片 */
.tarot-container::after {
    content: '';
    display: block;
    grid-column: span 3;
}

/* 卡罗牌卡片 */
.tarot-card {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%),
        linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 100% 100%, 400% 400%;
    background-position: center, 0% 50%;
    border-radius: 20px;
    padding: 30px;
    box-shadow:
        0 20px 60px rgba(0, 0, 0, 0.3),
        0 0 100px rgba(102, 126, 234, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transform: translateZ(0);
    transition: all 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    animation: cardEntry 0.8s ease-out;
    animation-fill-mode: both;
}

@keyframes cardEntry {
    from {
        opacity: 0;
        transform: translateY(50px) translateZ(-50px) rotateX(-15deg);
    }

    to {
        opacity: 1;
        transform: translateY(0) translateZ(0) rotateX(0deg);
    }
}

.tarot-card:nth-child(1) {
    animation-delay: 0.1s;
}

.tarot-card:nth-child(2) {
    animation-delay: 0.2s;
}

.tarot-card:nth-child(3) {
    animation-delay: 0.3s;
}

.tarot-card:nth-child(4) {
    animation-delay: 0.4s;
}

.tarot-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

.tarot-card::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
    pointer-events: none;
    border-radius: 18px;
}

.tarot-card:hover {
    transform: translateY(-10px) translateZ(20px) rotateX(5deg) rotateY(5deg);
    box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.4),
        0 0 120px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.3);
    background-position: center, 100% 50%;
}

/* 卡片标题 */
.tarot-card h3 {
    color: #FFD700;
    font-size: 1.5em;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
}

.tarot-card h3::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 40px;
    height: 2px;
    background: linear-gradient(90deg, transparent, #FFD700, transparent);
}

/* 卡片内容 */
.tarot-card-content {
    color: rgba(255, 255, 255, 0.9);
    line-height: 1.6;
    font-size: 1em;
}

.tarot-card p {
    margin-bottom: 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.tarot-card p:last-child {
    margin-bottom: 0;
}

/* 卡片图标 */
.tarot-card-icon {
    position: absolute;
    top: 20px;
    right: 20px;
    font-size: 2em;
    opacity: 0.3;
    transition: all 0.3s ease;
}

.tarot-card:hover .tarot-card-icon {
    opacity: 0.6;
    transform: scale(1.2) rotate(15deg);
}

/* 神位序列卡片 */
.sequence-card {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.15) 0%, rgba(255, 255, 255, 0.08) 100%),
        linear-gradient(45deg, #ff6b6b, #ee5a24, #feca57, #ff9ff3);
    background-size: 100% 100%, 400% 400%;
    background-position: center, 0% 50%;
    animation-delay: 0.5s;
}

.sequence-card .sequence-display {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
    margin-top: 20px;
}

.sequence-item {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 12px;
    padding: 15px;
    min-width: 80px;
    text-align: center;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.3s ease;
}

.sequence-item:hover {
    transform: translateY(-5px) scale(1.05);
    background: rgba(255, 255, 255, 0.2);
    box-shadow: 0 10px 20px rgba(0, 0, 0, 0.2);
}

.sequence-number {
    font-size: 0.9em;
    color: #FFD700;
    margin-bottom: 8px;
    font-weight: bold;
}

.sequence-god {
    font-weight: bold;
    font-size: 1.1em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 总体分析卡片 */
.analysis-card {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%),
        linear-gradient(45deg, #00b894, #00cec9, #55a3ff, #26de81);
    background-size: 100% 100%, 400% 400%;
    background-position: center, 0% 50%;
    animation-delay: 0.6s;
}

.analysis-card .overall-analysis {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 15px;
    padding: 25px;
    margin-top: 20px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.analysis-card .overall-analysis p {
    font-size: 1.1em;
    line-height: 1.8;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

/* 详细解释卡片 */
.details-card {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.08) 0%, rgba(255, 255, 255, 0.04) 100%),
        linear-gradient(45deg, #a29bfe, #6c5ce7, #fd79a8, #fdcb6e);
    background-size: 100% 100%, 400% 400%;
    background-position: center, 0% 50%;
    animation-delay: 0.7s;
}

.god-details {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.god-detail {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
}

.god-detail:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.god-detail h4 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.2em;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.god-detail p {
    margin-bottom: 12px;
    line-height: 1.6;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

.god-detail p:last-child {
    margin-bottom: 0;
}

.god-detail strong {
    color: #FFD700;
    font-weight: 600;
}

/* 计算过程卡片 - 优化配色 */
.process-card {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.08) 100%),
        linear-gradient(45deg, #74b9ff, #0984e3, #6c5ce7, #a29bfe);
    background-size: 100% 100%, 400% 400%;
    background-position: center, 0% 50%;
    animation-delay: 0.4s;
    background-size: 100% 100%, 400% 400%;
    background-position: center, 0% 50%;
    animation-delay: 0.4s;
}

.process-steps {
    display: grid;
    gap: 20px;
    margin-top: 20px;
}

.process-step {
    background: rgba(255, 255, 255, 0.08);
    border-radius: 15px;
    padding: 25px;
    backdrop-filter: blur(5px);
    border: 1px solid rgba(255, 255, 255, 0.15);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.process-step::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 4px;
    height: 100%;
    background: linear-gradient(180deg, #FFD700, #FFA500);
    opacity: 0.7;
}

.process-step:hover {
    background: rgba(255, 255, 255, 0.12);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(0, 0, 0, 0.2);
}

.step-number {
    font-weight: bold;
    color: #FFD700;
    margin-bottom: 12px;
    font-size: 1.1em;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.step-calculation {
    font-size: 1.2em;
    margin-bottom: 12px;
    color: #FFD700;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.step-interpretation {
    line-height: 1.6;
    color: rgba(255, 255, 255, 0.9);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
}

/* 计算过程 */
.process-steps {
    margin-bottom: 30px;
}

.process-step {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 15px;
    backdrop-filter: blur(10px);
}

.step-number {
    font-weight: bold;
    color: #FFD700;
    margin-bottom: 10px;
}

.step-calculation {
    font-size: 1.1em;
    margin-bottom: 10px;
    color: #FFD700;
}

.step-interpretation {
    line-height: 1.6;
}

/* 神位序列 */
.god-sequence {
    margin-bottom: 30px;
}

.god-sequence h4 {
    margin-bottom: 15px;
    color: #FFD700;
}

.sequence-display {
    display: flex;
    justify-content: center;
    gap: 15px;
    flex-wrap: wrap;
}

.sequence-item {
    text-align: center;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 15px;
    min-width: 80px;
}

.sequence-number {
    font-size: 0.9em;
    color: #FFD700;
    margin-bottom: 5px;
}

.sequence-god {
    font-weight: bold;
    font-size: 1.1em;
}

/* 结果分析 */
.overall-analysis {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    margin-bottom: 30px;
    backdrop-filter: blur(10px);
}

.overall-analysis p {
    font-size: 1.1em;
    line-height: 1.6;
}

.god-details {
    display: grid;
    gap: 20px;
}

.god-detail {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 10px;
    padding: 20px;
    backdrop-filter: blur(10px);
}

.god-detail h4 {
    color: #FFD700;
    margin-bottom: 15px;
    font-size: 1.3em;
}

.god-detail p {
    margin-bottom: 10px;
    line-height: 1.6;
}

.god-detail p:last-child {
    margin-bottom: 0;
}

.god-detail strong {
    color: #FFD700;
}

/* 页脚 */
footer {
    text-align: center;
    color: white;
    opacity: 0.9;
    font-size: 1em;
    padding: 30px 0;
    position: relative;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

footer::before {
    content: '';
    position: absolute;
    top: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 100px;
    height: 2px;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.5), transparent);
}

footer p {
    animation: fadeInUp 1s ease-out 0.5s both;
    line-height: 1.6;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .container {
        padding: 15px;
    }

    main {
        padding: 30px 20px;
        margin-bottom: 20px;
    }

    header h1 {
        font-size: 2.5em;
        margin-bottom: 10px;
    }

    header p {
        font-size: 1.1em;
    }

    .question-options {
        grid-template-columns: repeat(3, 1fr);
        gap: 15px;
        justify-content: center;
    }

    .question-btn {
        padding: 15px 20px;
        font-size: 1em;
    }

    .number-section h2 {
        font-size: 1.8em;
    }

    .number-inputs {
        flex-direction: column;
        align-items: center;
        gap: 15px;
    }

    .number-inputs input {
        width: 120px;
        height: 120px;
        font-size: 1.5em;
        padding: 15px;
    }

    #calculateBtn {
        padding: 20px 40px;
        font-size: 1.2em;
        min-width: 180px;
    }

    .finger-guide {
        padding: 20px;
        margin-bottom: 30px;
    }

    .finger-guide h3 {
        font-size: 1.5em;
    }

    .finger-reference-image img {
        max-width: 100%;
        border-radius: 12px;
    }

    .finger-positions {
        grid-template-columns: repeat(2, 1fr);
        gap: 15px;
    }

    .finger-position {
        padding: 20px 15px;
    }

    .result-section h2 {
        font-size: 2.2em;
        margin-bottom: 30px;
    }

    .tarot-container {
        grid-template-columns: repeat(2, 1fr);
        gap: 20px;
    }

    .tarot-container::after {
        grid-column: span 2;
    }

    .tarot-card {
        padding: 25px 20px;
        margin-bottom: 20px;
    }

    .tarot-card h3 {
        font-size: 1.5em;
    }

    .sequence-display {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .sequence-item {
        min-width: 100px;
        padding: 12px;
    }

    .finger-visual {
        flex-direction: column;
        gap: 20px;
    }

    .hand {
        margin-right: 0;
        margin-bottom: 20px;
        transform: scale(0.8);
    }

    .position-indicators {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
        gap: 10px;
    }

    .current-step-info {
        position: relative;
        top: auto;
        left: auto;
        transform: none;
        margin-top: 20px;
        max-width: 100%;
    }

    .animation-controls {
        flex-direction: column;
        gap: 10px;
    }

    .animation-controls button {
        width: 100%;
        max-width: 200px;
    }

    footer {
        font-size: 0.9em;
        padding: 20px 0;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    main {
        padding: 20px 15px;
    }

    header h1 {
        font-size: 2em;
    }

    .question-options {
        grid-template-columns: repeat(2, 1fr);
        justify-content: center;
    }

    .number-inputs input {
        width: 100px;
        height: 100px;
        font-size: 1.3em;
    }

    .finger-positions {
        grid-template-columns: 1fr;
    }

    .tarot-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .tarot-container::after {
        grid-column: span 1;
    }

    .tarot-card {
        padding: 20px 15px;
    }

    .tarot-card h3 {
        font-size: 1.3em;
    }

    .modal-body {
        grid-template-columns: 1fr;
        gap: 15px;
        min-height: 0;
        padding-bottom: 20px;
    }

    .modal-body::after {
        grid-column: span 1;
    }

    .god-detail {
        padding: 20px 15px;
    }

    .god-detail h4 {
        font-size: 1.2em;
    }

    .process-step {
        padding: 20px 15px;
    }

    /* 最终结果响应式设计 */
    .final-results-container {
        padding: 20px;
        margin-top: 30px;
    }

    .final-results-header h2 {
        font-size: 1.8em;
    }

    .final-sequence-section .sequence-item {
        min-width: 80px;
        padding: 15px;
    }

    .final-sequence-section .sequence-god {
        font-size: 1em;
    }

    .analysis-content {
        padding: 20px;
        font-size: 1em;
    }

    .final-details-section .god-detail {
        padding: 20px;
    }

    .final-details-section .god-detail h4 {
        font-size: 1.2em;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 10px;
    }

    main {
        padding: 20px 15px;
    }

    header h1 {
        font-size: 2em;
    }

    .question-options {
        grid-template-columns: repeat(2, 1fr);
        justify-content: center;
    }

    .number-inputs input {
        width: 100px;
        height: 100px;
        font-size: 1.3em;
    }

    .finger-positions {
        grid-template-columns: 1fr;
    }

    .tarot-container {
        grid-template-columns: 1fr;
        gap: 15px;
    }

    .tarot-container::after {
        grid-column: span 1;
    }

    .tarot-card {
        padding: 20px 15px;
    }

    .tarot-card h3 {
        font-size: 1.3em;
    }

    /* 最终结果小屏幕响应式设计 */
    .final-results-container {
        padding: 15px;
        margin-top: 20px;
    }

    .final-results-header h2 {
        font-size: 1.5em;
    }

    .final-results-header p {
        font-size: 0.9em;
    }

    .final-sequence-section h3 {
        font-size: 1.2em;
    }

    .final-sequence-section .sequence-display {
        flex-direction: column;
        align-items: center;
        gap: 10px;
    }

    .final-sequence-section .sequence-item {
        min-width: 100%;
        max-width: 200px;
        padding: 12px;
    }

    .final-analysis-section h3 {
        font-size: 1.2em;
    }

    .analysis-content {
        padding: 15px;
        font-size: 0.9em;
    }

    .final-details-section h3 {
        font-size: 1.2em;
    }

    .final-details-section .god-detail {
        padding: 15px;
    }

    .final-details-section .god-detail h4 {
        font-size: 1.1em;
        margin-bottom: 10px;
    }

    .final-details-section .god-nature,
    .final-details-section .god-interpretation {
        font-size: 0.85em;
        line-height: 1.4;
    }
}

/* 动画效果 */
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.result-section {
    animation: fadeIn 0.6s ease-out;
}

.process-step {
    animation: fadeIn 0.4s ease-out;
}

.god-detail {
    animation: fadeIn 0.5s ease-out;
}

/* 特殊颜色主题 */
.god-daan {
    color: #27ae60;
}

.god-liulian {
    color: #3498db;
}

.god-suxi {
    color: #e74c3c;
}

.god-chikou {
    color: #f39c12;
}

.god-xiaoji {
    color: #9b59b6;
}

.god-kongwang {
    color: #34495e;
}

/* 手指动画样式 */
.finger-animation {
    margin-bottom: 30px;
}

.finger-card {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.12) 0%, rgba(255, 255, 255, 0.06) 100%),
        linear-gradient(45deg, #74b9ff, #0984e3, #6c5ce7, #a29bfe);
    background-size: 100% 100%, 400% 400%;
    background-position: center, 0% 50%;
    animation-delay: 0.3s;
}

.finger-card h3 {
    color: #FFD700;
    margin-bottom: 20px;
    text-align: center;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.finger-visual {
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 200px;
    margin-bottom: 20px;
}

/* 手的样式 */
.hand {
    position: relative;
    width: 150px;
    height: 180px;
    margin-right: 50px;
}

.palm {
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 100px;
    background: linear-gradient(135deg, #fdbcb4 0%, #f4a09c 100%);
    border-radius: 20px 20px 30px 30px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.finger {
    position: absolute;
    background: linear-gradient(135deg, #fdbcb4 0%, #f4a09c 100%);
    border-radius: 15px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
    transition: all 0.3s ease;
}

.finger.thumb {
    width: 25px;
    height: 50px;
    bottom: 40px;
    left: 10px;
    transform: rotate(-30deg);
}

.finger.index {
    width: 20px;
    height: 70px;
    bottom: 80px;
    left: 35px;
}

.finger.middle {
    width: 20px;
    height: 80px;
    bottom: 85px;
    left: 55px;
}

.finger.ring {
    width: 18px;
    height: 70px;
    bottom: 80px;
    left: 75px;
}

.finger.pinky {
    width: 15px;
    height: 50px;
    bottom: 70px;
    left: 93px;
}

/* 位置指示器 */
.position-indicators {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.position-indicator {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 10px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    transition: all 0.3s ease;
}

.indicator-name {
    font-weight: bold;
    color: #FFD700;
    min-width: 50px;
}

.indicator-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.position-indicator.active .indicator-dot {
    background: #FFD700;
    box-shadow: 0 0 20px #FFD700;
    transform: scale(1.5);
}

.position-indicator.completed .indicator-dot {
    background: #27ae60;
    transform: scale(1.2);
}

.position-indicator.active {
    background: rgba(255, 215, 0, 0.2);
    transform: translateX(5px);
}

/* 当前步骤信息 */
.current-step-info {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 15px 25px;
    border-radius: 10px;
    text-align: center;
    z-index: 10;
    backdrop-filter: blur(10px);
}

.step-info-content {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.step-info-title {
    font-size: 1.2em;
    font-weight: bold;
    color: #FFD700;
}

.step-info-detail {
    font-size: 1em;
    color: white;
}

/* 动画控制按钮 */
.animation-controls {
    display: flex;
    justify-content: center;
    gap: 15px;
    margin-top: 20px;
}

.animation-controls button {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%),
        linear-gradient(45deg, #74b9ff, #0984e3);
    background-size: 100% 100%, 200% 200%;
    background-position: center, 0% 50%;
    color: white;
    border: 2px solid rgba(116, 185, 255, 0.3);
    padding: 12px 25px;
    border-radius: 12px;
    cursor: pointer;
    font-weight: bold;
    transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    box-shadow:
        0 8px 20px rgba(116, 185, 255, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    position: relative;
    overflow: hidden;
    transform-style: preserve-3d;
    transform: translateZ(0);
}

.animation-controls button::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    width: 0;
    height: 0;
    background: radial-gradient(circle, rgba(116, 185, 255, 0.4) 0%, transparent 70%);
    border-radius: 50%;
    transform: translate(-50%, -50%);
    transition: all 0.6s ease;
    pointer-events: none;
}

.animation-controls button:hover::before {
    width: 200px;
    height: 200px;
}

.animation-controls button:hover:not(:disabled) {
    transform: translateY(-3px) translateZ(8px) rotateX(3deg);
    box-shadow:
        0 15px 30px rgba(116, 185, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    border-color: rgba(116, 185, 255, 0.6);
    background-position: center, 100% 50%;
}

.animation-controls button:disabled {
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.9) 0%, rgba(255, 255, 255, 0.7) 100%),
        linear-gradient(45deg, #bdc3c7, #95a5a6);
    color: #95a5a6;
    cursor: not-allowed;
    box-shadow:
        0 5px 15px rgba(149, 165, 166, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    border-color: rgba(149, 165, 166, 0.3);
    transform: none;
}

.animation-controls button:not(:disabled)::after {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.4), transparent);
    transition: left 0.6s ease;
    pointer-events: none;
}

.animation-controls button:not(:disabled):hover::after {
    left: 100%;
}

/* 手指动画效果 */
.finger.active {
    animation: fingerTap 0.6s ease-in-out;
}

@keyframes fingerTap {

    0%,
    100% {
        transform: translateY(0);
    }

    50% {
        transform: translateY(-10px);
    }
}

/* 手掌动画效果 */
.hand.calculating .palm {
    animation: palmMove 0.8s ease-in-out infinite;
}

@keyframes palmMove {

    0%,
    100% {
        transform: translateX(-50%) rotate(0deg);
    }

    50% {
        transform: translateX(-50%) rotate(2deg);
    }
}

/* 弹出结果卡片模态框 */
.result-modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(102, 126, 234, 0.85);
    backdrop-filter: blur(8px);
    z-index: 1000;
    animation: modalFadeIn 0.5s ease-out;
}

@keyframes modalFadeIn {
    from {
        opacity: 0;
    }

    to {
        opacity: 1;
    }
}

.result-modal-content {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 90%;
    max-width: 800px;
    max-height: 85vh;
    background:
        linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%),
        linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    background-size: 100% 100%, 400% 400%;
    background-position: center, 0% 50%;
    border-radius: 25px;
    padding: 40px;
    box-shadow:
        0 30px 80px rgba(0, 0, 0, 0.5),
        0 0 150px rgba(102, 126, 234, 0.3),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(15px);
    border: 2px solid rgba(255, 255, 255, 0.2);
    overflow-y: auto;
    animation: modalSlideIn 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
    /* 确保滚动条正常工作 */
    -webkit-overflow-scrolling: touch;
    /* 为内容留出更多空间 */
    box-sizing: border-box;
    /* 自定义滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 215, 0, 0.3) transparent;
    /* 确保始终居中显示 */
    margin: 0;
    /* 当内容太多时，确保内容可以滚动但模态框保持在视口中心 */
    max-height: calc(100vh - 100px);
}

/* Webkit滚动条样式 */
.result-modal-content::-webkit-scrollbar {
    width: 8px;
}

.result-modal-content::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 4px;
}

.result-modal-content::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.3);
    border-radius: 4px;
    border: 2px solid transparent;
    background-clip: content-box;
}

.result-modal-content::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.5);
    background-clip: content-box;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translate(-50%, -60%) scale(0.8);
    }

    to {
        opacity: 1;
        transform: translate(-50%, -50%) scale(1);
    }
}

.modal-close-btn {
    position: absolute;
    top: 20px;
    right: 25px;
    background: rgba(255, 255, 255, 0.2);
    border: none;
    color: white;
    font-size: 2em;
    width: 50px;
    height: 50px;
    border-radius: 50%;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    backdrop-filter: blur(10px);
}

.modal-close-btn:hover {
    background: rgba(255, 255, 255, 0.3);
    transform: scale(1.1) rotate(90deg);
}

.modal-header {
    text-align: center;
    margin-bottom: 30px;
}

.modal-header h2 {
    color: #FFD700;
    font-size: 2.2em;
    margin-bottom: 10px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    background: linear-gradient(45deg, #FFD700, #FFA500, #FF6B6B, #4ECDC4);
    background-size: 400% 400%;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    animation: gradientShift 4s ease-in-out infinite;
}

.modal-body {
    display: flex;
    flex-direction: column;
    gap: 25px;
    /* 确保内容不会导致容器溢出 */
    min-height: 0;
    /* 为滚动内容留出空间 */
    padding-bottom: 20px;
}

/* 横向卡片布局样式 */
.modal-card-row {
    display: flex;
    gap: 20px;
    width: 100%;
    overflow-x: auto;
    padding-bottom: 10px;
    /* 横向滚动条样式 */
    scrollbar-width: thin;
    scrollbar-color: rgba(255, 215, 0, 0.3) transparent;
}

.modal-card-row::-webkit-scrollbar {
    height: 6px;
}

.modal-card-row::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 3px;
}

.modal-card-row::-webkit-scrollbar-thumb {
    background: rgba(255, 215, 0, 0.3);
    border-radius: 3px;
    border: 1px solid transparent;
    background-clip: content-box;
}

.modal-card-row::-webkit-scrollbar-thumb:hover {
    background: rgba(255, 215, 0, 0.5);
    background-clip: content-box;
}

.modal-tarot-card {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    padding: 25px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    transition: all 0.4s ease;
    position: relative;
    overflow: hidden;
    /* 确保卡片内容不会溢出 */
    word-wrap: break-word;
    word-break: break-word;
    /* 横向布局卡片样式 */
    flex: 0 0 auto;
    min-width: 280px;
    max-width: 350px;
}

.modal-tarot-card::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.1), transparent);
    animation: shimmer 3s infinite;
    pointer-events: none;
}

.modal-tarot-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.3);
    background: rgba(255, 255, 255, 0.15);
}

.modal-card-icon {
    position: absolute;
    top: 15px;
    right: 15px;
    font-size: 1.5em;
    opacity: 0.3;
}

.modal-tarot-card h3 {
    color: #FFD700;
    font-size: 1.3em;
    margin-bottom: 15px;
    text-align: center;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
}

.modal-card-content {
    color: rgba(255, 255, 255, 0.9);
    font-size: 0.95em;
    line-height: 1.6;
    /* 确保内容可以正确换行和滚动 */
    overflow-wrap: break-word;
    word-wrap: break-word;
    word-break: break-word;
    /* 防止内容溢出容器 */
    max-width: 100%;
    box-sizing: border-box;
}

/* 响应式手指动画 */
@media (max-width: 768px) {
    .finger-visual {
        flex-direction: column;
        gap: 20px;
    }

    .hand {
        margin-right: 0;
        margin-bottom: 20px;
    }

    .position-indicators {
        flex-direction: row;
        flex-wrap: wrap;
        justify-content: center;
    }

    .current-step-info {
        position: relative;
        top: auto;
        left: auto;
        transform: none;
        margin-top: 20px;
    }

    .result-modal-content {
        width: 95%;
        max-height: 90vh;
        padding: 25px;
        margin: 20px;
        box-sizing: border-box;
    }

    .modal-header h2 {
        font-size: 1.8em;
    }

    .modal-body {
        flex-direction: column;
        gap: 20px;
        min-height: 0;
        padding-bottom: 20px;
    }

    .modal-card-row {
        flex-direction: column;
        gap: 15px;
    }

    .modal-tarot-card {
        min-width: auto;
        max-width: 100%;
        padding: 20px;
    }

    .modal-tarot-card h3 {
        font-size: 1.2em;
    }
}

/* 
滚动性能优化 - 移除全局transform规则以避免影响交互 */

/* 优化动画性能 */
.final-results-container,
.final-sequence-section .sequence-item,
.analysis-content,
.final-details-section .god-detail {
    will-change: transform, opacity;
    transform: translateZ(0);
    backface-visibility: hidden;
}

/* 减少重绘和回流 */
.final-results-container::before,
.main::before,
body::before {
    will-change: transform;
    transform: translateZ(0);
}

/* 滚动容器优化 */
.container {
    contain: layout style paint;
}

/* 动画优化 */
@keyframes shimmer {
    0% {
        transform: translateX(-100%) translateY(-100%) rotate(45deg) translateZ(0);
    }

    100% {
        transform: translateX(100%) translateY(100%) rotate(45deg) translateZ(0);
    }
}

@keyframes backgroundMove {

    0%,
    100% {
        transform: translateX(0) translateY(0) translateZ(0);
    }

    25% {
        transform: translateX(-10px) translateY(-10px) translateZ(0);
    }

    50% {
        transform: translateX(10px) translateY(-5px) translateZ(0);
    }

    75% {
        transform: translateX(-5px) translateY(10px) translateZ(0);
    }
}

/* 减少动画频率以提升性能 */
@media (prefers-reduced-motion: reduce) {
    * {
        animation-duration: 0.01ms !important;
        animation-iteration-count: 1 !important;
        transition-duration: 0.01ms !important;
    }

    .final-results-container::before,
    main::before,
    body::before {
        animation: none !important;
    }
}

/* 移动设备滚动优化 */
@media (max-width: 768px) {
    body {
        -webkit-overflow-scrolling: touch;
        overflow-scrolling: touch;
    }

    /* 减少移动设备上的复杂效果 */
    .final-results-container,
    .final-sequence-section .sequence-item,
    .analysis-content,
    .final-details-section .god-detail {
        transform: none;
        will-change: auto;
        backdrop-filter: none;
    }

    /* 减少移动设备上的动画 */
    .final-results-container::before,
    main::before {
        animation: none;
    }

    /* 简化阴影效果 */
    .question-btn,
    .number-inputs input,
    #calculateBtn,
    .finger-position {
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    }
}

/*
 计算过程中的子卡片样式 - 非白色背景 */
.god-details .god-detail {
    background: linear-gradient(135deg, rgba(116, 185, 255, 0.15) 0%, rgba(108, 92, 231, 0.15) 100%) !important;
    border-radius: 15px;
    padding: 25px;
    box-shadow:
        0 6px 20px rgba(116, 185, 255, 0.2),
        inset 0 1px 0 rgba(255, 255, 255, 0.6);
    border: 1px solid rgba(116, 185, 255, 0.3) !important;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    will-change: transform;
    backdrop-filter: blur(8px);
}

.god-details .god-detail:hover {
    transform: translateY(-3px) translateZ(0);
    box-shadow:
        0 12px 28px rgba(116, 185, 255, 0.25),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    background: linear-gradient(135deg, rgba(116, 185, 255, 0.2) 0%, rgba(108, 92, 231, 0.2) 100%) !important;
}

.god-details .god-detail h4 {
    color: #2c3e50 !important;
    margin-bottom: 15px;
    font-size: 1.3em;
    font-weight: 500;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.god-details .god-detail .god-nature {
    color: #34495e !important;
    line-height: 1.6;
    font-size: 1em;
}

/* 为计算过程容器添加ID以便滚动定位 */
#calculation-process {
    scroll-margin-top: 20px;
}

#final-results {
    scroll-margin-top: 20px;
}

/* 增强计算
过程的动画效果 */
@keyframes slideInFromLeft {
    0% {
        opacity: 0;
        transform: translateX(-50px) translateY(20px);
    }

    100% {
        opacity: 1;
        transform: translateX(0) translateY(0);
    }
}

@keyframes slideInFromRight {
    0% {
        opacity: 0;
        transform: translateX(50px) translateY(20px);
    }

    100% {
        opacity: 1;
        transform: translateX(0) translateY(0);
    }
}

@keyframes fadeInUp {
    0% {
        opacity: 0;
        transform: translateY(30px);
    }

    100% {
        opacity: 1;
        transform: translateY(0);
    }
}

/* 简化计算过程的卡片动画 - 移除复杂动画以提升滚动性能 */
.god-details .god-detail:nth-child(odd),
.god-details .god-detail:nth-child(even) {
    /* 移除入场动画以提升性能 */
    animation: none;
}

/* 简化最终结果动画 */
#final-results {
    /* 移除动画以提升滚动性能 */
    animation: none;
}

/* 添加一个脉冲效果给正在计算的提示 */
@keyframes pulse {
    0% {
        opacity: 1;
        transform: scale(1);
    }

    50% {
        opacity: 0.7;
        transform: scale(1.05);
    }

    100% {
        opacity: 1;
        transform: scale(1);
    }
}

.calculating-indicator {
    animation: pulse 1.5s ease-in-out infinite;
    color: #74b9ff;
    font-weight: bold;
    text-align: center;
    margin: 20px 0;
    font-size: 1.1em;
}

/* 优化滚动指示器 - 减少transform使用 */
.scroll-indicator {
    position: fixed;
    right: 30px;
    top: 50%;
    margin-top: -50px; /* 替代 transform: translateY(-50%) */
    z-index: 1000;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.scroll-dot {
    width: 12px;
    height: 12px;
    border-radius: 50%;
    background: rgba(116, 185, 255, 0.3);
    transition: all 0.3s ease;
    cursor: pointer;
}

.scroll-dot.active {
    background: #74b9ff;
    /* 移除transform scale以减少重绘 */
    width: 14px;
    height: 14px;
}

.scroll-dot:hover {
    background: #74b9ff;
    /* 移除transform scale以减少重绘 */
    width: 13px;
    height: 13px;
}

/* 移动设备上隐藏滚动指示器 */
@media (max-width: 768px) {
    .scroll-indicator {
        display: none;
    }
}

/* 设置面板样式 */
.settings-modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(5px);
}

.settings-modal-content {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    margin: 5% auto;
    padding: 0;
    border-radius: 20px;
    width: 90%;
    max-width: 500px;
    max-height: 80vh;
    overflow-y: auto;
    position: relative;
    border: 1px solid rgba(255, 255, 255, 0.2);
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.settings-body {
    padding: 30px;
}

.setting-item {
    margin-bottom: 25px;
}

.setting-item label {
    display: block;
    color: white;
    font-weight: 600;
    margin-bottom: 8px;
    font-size: 1.1em;
}

.setting-item input,
.setting-item select {
    width: 100%;
    padding: 12px 15px;
    border: 1px solid rgba(255, 255, 255, 0.3);
    border-radius: 10px;
    background: rgba(255, 255, 255, 0.1);
    color: white;
    font-size: 1em;
    backdrop-filter: blur(10px);
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.setting-item input::placeholder {
    color: rgba(255, 255, 255, 0.6);
}

.setting-item input:focus,
.setting-item select:focus {
    outline: none;
    border-color: rgba(255, 255, 255, 0.6);
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

.setting-item small {
    display: block;
    color: rgba(255, 255, 255, 0.7);
    font-size: 0.9em;
    margin-top: 5px;
}

.settings-actions {
    display: flex;
    gap: 15px;
    margin-top: 30px;
}

.save-btn,
.test-btn {
    flex: 1;
    padding: 12px 20px;
    border: none;
    border-radius: 10px;
    font-size: 1em;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
}

.save-btn {
    background: linear-gradient(135deg, #4CAF50, #45a049);
    color: white;
}

.save-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(76, 175, 80, 0.4);
}

.test-btn {
    background: linear-gradient(135deg, #2196F3, #1976D2);
    color: white;
}

.test-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(33, 150, 243, 0.4);
}

/* AI 解答区域样式 */
.ai-answer-section {
    margin-top: 30px;
    padding: 25px;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05));
    border-radius: 15px;
    border: 1px solid rgba(255, 255, 255, 0.2);
    backdrop-filter: blur(10px);
}

.ai-answer-section h3 {
    color: #4CAF50;
    margin-bottom: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
}

.ai-answer-section h3::before {
    content: "🤖";
    font-size: 1.2em;
}



.ai-answer-content {
    color: #2c3e50;
    line-height: 1.6;
    font-size: 1.05em;
    background: rgba(255, 255, 255, 0.9);
    padding: 20px;
    border-radius: 10px;
    margin-top: 15px;
}

.ai-answer-content h2 {
    color: #667eea;
    font-size: 1.3em;
    margin: 20px 0 10px 0;
    font-weight: 600;
}

.ai-answer-content h3 {
    color: #764ba2;
    font-size: 1.2em;
    margin: 15px 0 8px 0;
    font-weight: 600;
}

.ai-answer-content strong {
    color: #e74c3c;
    font-weight: 600;
}

.ai-answer-content em {
    color: #8e44ad;
    font-style: italic;
}

.ai-answer-content ul {
    margin: 10px 0;
    padding-left: 20px;
}

.ai-answer-content li {
    margin: 5px 0;
    color: #2c3e50;
}

.ai-error {
    color: #ff6b6b;
    background: rgba(255, 107, 107, 0.1);
    padding: 15px;
    border-radius: 10px;
    border: 1px solid rgba(255, 107, 107, 0.3);
}

/* AI解答按钮样式 */
.ai-button-container {
    text-align: center;
    margin: 30px 0;
    padding: 20px;
}

.ai-answer-btn {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
    border-radius: 20px;
    padding: 20px 40px;
    color: white;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 8px 25px rgba(102, 126, 234, 0.3),
        0 3px 10px rgba(0, 0, 0, 0.1);
    position: relative;
    overflow: hidden;
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 5px;
    min-width: 180px;
}

.ai-answer-btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.ai-answer-btn:hover::before {
    left: 100%;
}

.ai-answer-btn:hover {
    transform: translateY(-3px);
    box-shadow:
        0 12px 35px rgba(102, 126, 234, 0.4),
        0 5px 15px rgba(0, 0, 0, 0.15);
}

.ai-answer-btn:active {
    transform: translateY(-1px);
}

.ai-btn-icon {
    font-size: 24px;
    margin-bottom: 5px;
}

.ai-btn-text {
    font-size: 16px;
    font-weight: 600;
}

.ai-btn-subtitle {
    font-size: 12px;
    opacity: 0.9;
    font-weight: 400;
}

/* AI弹窗样式 - 卡牌风格 */
.ai-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    backdrop-filter: blur(10px);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 10000;
    opacity: 0;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    padding: 20px;
}

.ai-modal-overlay.show {
    opacity: 1;
}

.ai-modal-overlay.closing {
    opacity: 0;
}

.ai-modal-card {
    background: linear-gradient(135deg, #1a1a2e 0%, #16213e 50%, #0f3460 100%);
    border-radius: 25px;
    max-width: 900px;
    width: 100%;
    max-height: 90vh;
    overflow: hidden;
    box-shadow:
        0 25px 50px rgba(0, 0, 0, 0.5),
        0 0 0 1px rgba(255, 255, 255, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.2);
    position: relative;
    transform: scale(0.8) translateY(50px);
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.ai-modal-overlay.show .ai-modal-card {
    transform: scale(1) translateY(0);
}

/* 卡牌静态背景 */
.ai-modal-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 25px;
    z-index: -1;
}

.ai-modal-header {
    padding: 30px 30px 20px;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
    display: flex;
    justify-content: center;
    align-items: center;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.1) 0%, rgba(255, 255, 255, 0.05) 100%);
    position: relative;
}

.ai-modal-title {
    display: flex;
    align-items: center;
    gap: 15px;
}

.ai-modal-icon {
    font-size: 32px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.ai-modal-title h2 {
    color: #ffffff;
    margin: 0;
    font-size: 24px;
    font-weight: 600;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.ai-modal-close {
    background: rgba(255, 255, 255, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    color: #ffffff;
    font-size: 24px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    right: 30px;
    top: 50%;
    transform: translateY(-50%);
}

.ai-modal-close:hover {
    background: rgba(255, 255, 255, 0.2);
    transform: rotate(90deg);
}

.ai-modal-body {
    padding: 35px;
    max-height: 65vh;
    overflow-y: auto;
    color: #ffffff;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
}

/* AI加载动画 */
.ai-loading {
    text-align: center;
    padding: 60px 20px;
    color: #ffffff;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.02) 0%, rgba(255, 255, 255, 0.01) 100%);
    border-radius: 20px;
    margin: 20px;
}

.ai-spinner {
    width: 60px;
    height: 60px;
    border: 4px solid rgba(255, 255, 255, 0.1);
    border-top: 4px solid #667eea;
    border-right: 4px solid #764ba2;
    border-radius: 50%;
    animation: spin 1.2s linear infinite;
    margin: 0 auto 25px;
    position: relative;
}

.ai-spinner::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 2px solid rgba(255, 255, 255, 0.2);
    border-top: 2px solid #f093fb;
    border-radius: 50%;
    animation: spin 0.8s linear infinite reverse;
}

.ai-loading p {
    font-size: 16px;
    margin: 0;
    opacity: 0.9;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: 500;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* AI解答内容样式 */
.ai-answer-content {
    line-height: 1.9;
    font-size: 16px;
    color: #ffffff;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.03) 0%, rgba(255, 255, 255, 0.01) 100%);
    border-radius: 20px;
    padding: 25px;
    border: 1px solid rgba(255, 255, 255, 0.08);
    backdrop-filter: blur(10px);
}

.ai-answer-content h2 {
    color: #f093fb;
    font-size: 24px;
    margin: 30px 0 20px;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
    position: relative;
    padding-left: 20px;
}

.ai-answer-content h2::before {
    content: '✨';
    position: absolute;
    left: 0;
    top: 0;
    font-size: 20px;
    background: linear-gradient(135deg, #f093fb, #f5576c);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.ai-answer-content h3 {
    color: #667eea;
    font-size: 20px;
    margin: 25px 0 15px;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    position: relative;
    padding-left: 20px;
}

.ai-answer-content h3::before {
    content: '🔮';
    position: absolute;
    left: 0;
    top: 0;
    font-size: 16px;
}

.ai-answer-content strong {
    color: #f5576c;
    font-weight: 600;
    background: linear-gradient(135deg, #f5576c, #f093fb);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.ai-answer-content em {
    color: #764ba2;
    font-style: italic;
    background: linear-gradient(135deg, #764ba2, #667eea);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.ai-answer-content p {
    margin: 18px 0;
    padding: 15px 20px;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 12px;
    border-left: 3px solid rgba(102, 126, 234, 0.5);
}

.ai-answer-content li {
    margin: 12px 0;
    padding: 10px 15px 10px 35px;
    position: relative;
    color: #ffffff;
    background: rgba(255, 255, 255, 0.02);
    border-radius: 10px;
}

.ai-answer-content li::before {
    content: '🌟';
    position: absolute;
    left: 10px;
    top: 12px;
    font-size: 14px;
    background: linear-gradient(135deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .ai-modal-card {
        margin: 10px;
        border-radius: 20px;
        max-height: 95vh;
    }

    .ai-modal-header {
        padding: 20px 20px 15px;
    }

    .ai-modal-close {
        right: 20px;
        width: 35px;
        height: 35px;
    }

    .ai-modal-title h2 {
        font-size: 20px;
    }

    .ai-modal-icon {
        font-size: 28px;
    }

    .ai-modal-body {
        padding: 20px;
        max-height: 70vh;
    }

    .ai-answer-btn-small {
        padding: 6px 12px;
        font-size: 0.8em;
        border-radius: 15px;
    }

    .ai-btn-text-small {
        display: none;
    }

    .ai-btn-icon-small {
        font-size: 1.1em;
    }

    .ai-answer-btn {
        padding: 15px 30px;
        min-width: 150px;
    }

    .ai-btn-icon {
        font-size: 20px;
    }

    .ai-btn-text {
        font-size: 14px;
    }

    .ai-btn-subtitle {
        font-size: 11px;
    }
}